# App Studio

A Flutter-based visual mobile app development platform inspired by Sketchware Pro. This platform allows users to visually build Android applications on their mobile devices through a block-based programming interface—no traditional coding required.

## Features

- **Visual Block Programming**: Drag-and-drop interface for creating app logic
- **Real-time Preview**: See your app come to life as you build it
- **Material 3 Design**: Modern, flat, minimalistic design system
- **Code Generation**: Auto-generated Dart code with export options
- **Firebase Integration**: Built-in support for Firebase services
- **Plugin System**: Extensible architecture for custom blocks and widgets
- **Multi-platform Support**: Build for Android with Flutter

## Architecture

This project follows a **Feature-Based Clean Architecture** pattern with the following structure:

```
lib/
├── core/                    # Core functionality and utilities
│   ├── constants/          # App-wide constants
│   ├── entities/           # Base entities and value objects
│   ├── error/              # Error handling and failures
│   ├── network/            # Network utilities
│   ├── router/             # App routing configuration
│   └── usecases/           # Base use case interfaces
├── features/               # Feature modules
│   ├── block_editor/       # Visual block programming
│   ├── ui_builder/         # Drag-drop UI construction
│   ├── code_generator/     # Dart code generation
│   ├── preview_engine/     # Real-time app preview
│   ├── project_manager/    # Project CRUD operations
│   └── widget_library/     # Available Flutter widgets
├── shared/                 # Shared utilities and services
│   ├── models/            # Data models and DTOs
│   ├── services/          # Cross-cutting services
│   └── widgets/           # Reusable UI components
└── injection_container.dart # Dependency injection setup
```

Each feature follows the Clean Architecture pattern:

- **Data Layer**: Data sources, repositories, models
- **Domain Layer**: Entities, use cases, repository interfaces
- **Presentation Layer**: BLoC, pages, widgets
