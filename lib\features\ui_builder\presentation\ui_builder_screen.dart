import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/canvas/canvas_widget.dart';
import '../../../core/canvas/canvas_controller.dart';
import '../../../core/preview/preview_engine.dart';
import '../../../core/entities/block_entity.dart';
import '../../../core/entities/project_entity.dart';
import '../../../shared/widgets/atoms/app_button.dart';
import 'widgets/widget_palette.dart';
import 'widgets/properties_panel.dart';

/// Main UI Builder screen with canvas, widget palette, and properties panel
/// 
/// Provides the core visual development interface with drag-and-drop functionality,
/// real-time preview, and property editing capabilities.
class UIBuilderScreen extends StatefulWidget {
  const UIBuilderScreen({
    super.key,
    required this.project,
    this.screenId,
  });

  final ProjectEntity project;
  final String? screenId;

  @override
  State<UIBuilderScreen> createState() => _UIBuilderScreenState();
}

class _UIBuilderScreenState extends State<UIBuilderScreen> {
  late CanvasController _canvasController;
  late PreviewEngine _previewEngine;
  
  bool _showPreview = false;
  bool _showGrid = true;
  bool _showProperties = true;
  String? _selectedBlockId;

  @override
  void initState() {
    super.initState();
    _canvasController = CanvasController();
    _previewEngine = PreviewEngine();
    
    // Load existing blocks for the screen
    _loadScreenBlocks();
  }

  @override
  void dispose() {
    _canvasController.dispose();
    super.dispose();
  }

  void _loadScreenBlocks() {
    // TODO: Load blocks from project data for the current screen
    // For now, add some sample blocks for demonstration
    _addSampleBlocks();
  }

  void _addSampleBlocks() {
    // Sample container block
    final containerBlock = BlockEntity(
      id: 'container_1',
      type: BlockType.container,
      position: const BlockPosition(x: 100, y: 100, z: 0),
      properties: {
        'width': 200.0,
        'height': 150.0,
        'backgroundColor': 0xFF2196F3,
        'borderRadius': 12.0,
        'padding': {'all': 16.0},
      },
      connections: [],
    );

    // Sample text block
    final textBlock = BlockEntity(
      id: 'text_1',
      type: BlockType.text,
      position: const BlockPosition(x: 120, y: 130, z: 1),
      properties: {
        'text': 'Hello App Studio!',
        'fontSize': 18.0,
        'color': 0xFFFFFFFF,
        'fontWeight': 600,
      },
      connections: [],
      parentId: 'container_1',
    );

    // Sample button block
    final buttonBlock = BlockEntity(
      id: 'button_1',
      type: BlockType.button,
      position: const BlockPosition(x: 350, y: 200, z: 0),
      properties: {
        'text': 'Click Me',
        'backgroundColor': 0xFF4CAF50,
        'textColor': 0xFFFFFFFF,
        'borderRadius': 8.0,
        'padding': {'horizontal': 24.0, 'vertical': 12.0},
      },
      connections: [],
    );

    _canvasController.addBlock(containerBlock);
    _canvasController.addBlock(textBlock);
    _canvasController.addBlock(buttonBlock);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.colors.surface,
      appBar: _buildAppBar(),
      body: _showPreview ? _buildPreviewMode() : _buildDesignMode(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: DesignSystem.colors.surface,
      elevation: 0,
      title: Text(
        'UI Builder',
        style: DesignSystem.typography.titleLarge,
      ),
      actions: [
        IconButton(
          icon: Icon(_showGrid ? Ionicons.grid : Ionicons.grid_outline),
          onPressed: () => setState(() => _showGrid = !_showGrid),
          tooltip: 'Toggle Grid',
        ),
        IconButton(
          icon: Icon(_showProperties ? Ionicons.settings : Ionicons.settings_outline),
          onPressed: () => setState(() => _showProperties = !_showProperties),
          tooltip: 'Toggle Properties',
        ),
        IconButton(
          icon: Icon(_showPreview ? Ionicons.code : Ionicons.eye),
          onPressed: () => setState(() => _showPreview = !_showPreview),
          tooltip: _showPreview ? 'Design Mode' : 'Preview Mode',
        ),
        SizedBox(width: DesignSystem.spacing.sm),
      ],
    );
  }

  Widget _buildDesignMode() {
    return Row(
      children: [
        // Widget Palette
        Container(
          width: DesignSystem.components.sidebarWidth,
          decoration: BoxDecoration(
            color: DesignSystem.colors.surfaceVariant,
            border: Border(
              right: BorderSide(
                color: DesignSystem.colors.outline,
                width: 1,
              ),
            ),
          ),
          child: const WidgetPalette(),
        ),
        
        // Canvas Area
        Expanded(
          child: CanvasWidget(
            controller: _canvasController,
            showGrid: _showGrid,
            onBlockTap: _onBlockTap,
            onBlockDoubleTap: _onBlockDoubleTap,
            onCanvasTap: _onCanvasTap,
          ),
        ),
        
        // Properties Panel
        if (_showProperties)
          Container(
            width: DesignSystem.components.propertiesPanelWidth,
            decoration: BoxDecoration(
              color: DesignSystem.colors.surfaceVariant,
              border: Border(
                left: BorderSide(
                  color: DesignSystem.colors.outline,
                  width: 1,
                ),
              ),
            ),
            child: PropertiesPanel(
              selectedBlockId: _selectedBlockId,
              canvasController: _canvasController,
              onPropertyChanged: _onPropertyChanged,
            ),
          ),
      ],
    );
  }

  Widget _buildPreviewMode() {
    final blocks = _canvasController.blocks.values.toList();
    
    return Container(
      color: DesignSystem.colors.canvasBackground,
      child: Center(
        child: Container(
          width: 375, // iPhone width for preview
          height: 667, // iPhone height for preview
          decoration: BoxDecoration(
            color: DesignSystem.colors.surface,
            borderRadius: BorderRadius.circular(DesignSystem.components.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: DesignSystem.colors.blockShadow,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          clipBehavior: Clip.antiAlias,
          child: _previewEngine.renderScreen(
            blocks: blocks,
            settings: widget.project.settings,
            screenId: widget.screenId,
          ),
        ),
      ),
    );
  }

  void _onBlockTap(String blockId) {
    setState(() {
      _selectedBlockId = blockId;
    });
    _canvasController.selectBlock(blockId);
  }

  void _onBlockDoubleTap(String blockId) {
    // TODO: Open block editor dialog
    debugPrint('Double tapped block: $blockId');
  }

  void _onCanvasTap(Offset position) {
    setState(() {
      _selectedBlockId = null;
    });
  }

  void _onPropertyChanged(String blockId, String property, dynamic value) {
    final block = _canvasController.blocks[blockId];
    if (block != null) {
      final updatedProperties = Map<String, dynamic>.from(block.properties);
      updatedProperties[property] = value;
      
      final updatedBlock = block.copyWith(properties: updatedProperties);
      _canvasController.updateBlock(blockId, updatedBlock);
    }
  }
}
