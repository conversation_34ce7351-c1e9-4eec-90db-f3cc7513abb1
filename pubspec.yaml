name: app_studio
description: A Flutter-based visual mobile app development platform inspired by Sketchware Pro.
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # State Management & Architecture
  flutter_bloc: ^9.1.1
  get_it: ^8.1.0
  injectable: ^2.5.1
  equatable: ^2.0.7
  dartz: ^0.10.1

  # Drag & Drop UI
  flutter_draggable_gridview: ^0.0.13
  flutter_staggered_grid_view: ^0.7.0
  super_drag_and_drop: ^0.9.1

  # Code Generation & Analysis
  json_annotation: ^4.9.0
  code_builder: ^4.10.1
  analyzer: ^8.0.0
  dart_style: ^3.1.1

  # File & Project Management (temporarily disabled due to compatibility issues)
  path_provider: ^2.1.5
  # file_picker: ^6.1.1
  # archive: ^3.4.9
  shared_preferences: ^2.5.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI Components & Theming
  flutter_colorpicker: ^1.1.0
  flutter_highlight: ^0.7.0
  code_text_field: ^1.1.0
  ionicons: ^0.2.2
  flutter_animate: ^4.5.2
  lottie: ^3.3.1

  # Backend Integration
  dio: ^5.8.0+1
  http: ^1.4.0
  firebase_core: ^4.0.0
  cloud_firestore: ^6.0.0
  firebase_auth: ^6.0.0

  # Utilities
  uuid: ^4.5.1
  intl: ^0.20.2
  collection: ^1.19.1
  go_router: ^16.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Quality & Testing
  flutter_lints: ^6.0.0
  very_good_analysis: ^9.0.0
  build_runner: ^2.6.0
  injectable_generator: ^2.8.0
  json_serializable: ^6.10.0
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^10.0.0
  mocktail: ^1.0.4
  golden_toolkit: ^0.15.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/templates/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
