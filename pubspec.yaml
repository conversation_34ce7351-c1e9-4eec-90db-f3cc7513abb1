name: app_studio
description: A Flutter-based visual mobile app development platform inspired by Sketchware Pro.
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # State Management & Architecture
  flutter_bloc: ^8.1.3
  get_it: ^7.6.4
  injectable: ^2.3.2
  equatable: ^2.0.5
  dartz: ^0.10.1

  # Drag & Drop UI
  flutter_draggable_gridview: ^0.0.13
  flutter_staggered_grid_view: ^0.7.0
  super_drag_and_drop: ^0.8.17

  # Code Generation & Analysis
  json_annotation: ^4.8.1
  code_builder: ^4.7.0
  analyzer: ^6.2.0
  dart_style: ^2.3.2

  # File & Project Management (temporarily disabled due to compatibility issues)
  path_provider: ^2.1.1
  # file_picker: ^6.1.1
  # archive: ^3.4.9
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI Components & Theming
  flutter_colorpicker: ^1.0.3
  flutter_highlight: ^0.7.0
  code_text_field: ^1.1.0
  ionicons: ^0.2.2
  flutter_animate: ^4.2.0+1
  lottie: ^2.7.0

  # Backend Integration
  dio: ^5.3.2
  http: ^1.1.0
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3

  # Utilities
  uuid: ^4.1.0
  intl: ^0.19.0
  collection: ^1.18.0
  go_router: ^14.2.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Quality & Testing
  flutter_lints: ^5.0.0
  very_good_analysis: ^5.1.0
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  golden_toolkit: ^0.15.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/templates/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
