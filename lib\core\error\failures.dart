import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  const Failure([this.message]);

  final String? message;

  @override
  List<Object?> get props => [message];
}

/// General failures
class ServerFailure extends Failure {
  const ServerFailure([super.message]);
}

class CacheFailure extends Failure {
  const CacheFailure([super.message]);
}

class NetworkFailure extends Failure {
  const NetworkFailure([super.message]);
}

class ValidationFailure extends Failure {
  const ValidationFailure([super.message]);
}

/// Platform-specific failures
class CodeGenerationFailure extends Failure {
  const CodeGenerationFailure([super.message]);
}

class ProjectLoadFailure extends Failure {
  const ProjectLoadFailure([super.message]);
}

class ProjectSaveFailure extends Failure {
  const ProjectSaveFailure([super.message]);
}

class BlockValidationFailure extends Failure {
  const BlockValidationFailure([super.message]);
}

class PreviewRenderFailure extends Failure {
  const PreviewRenderFailure([super.message]);
}

class WidgetCreationFailure extends Failure {
  const WidgetCreationFailure([super.message]);
}

class ProjectValidationFailure extends Failure {
  const ProjectValidationFailure([super.message]);
}

class UnimplementedFailure extends Failure {
  const UnimplementedFailure([super.message]);
}
