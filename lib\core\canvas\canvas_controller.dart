import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import '../entities/block_entity.dart';
import '../constants/app_constants.dart';

/// Controller for managing canvas state and operations
/// 
/// Handles block positioning, selection, drag operations, and canvas transformations
/// with support for undo/redo, snapping, and virtualization for performance.
class CanvasController extends ChangeNotifier {
  CanvasController({
    this.gridSize = AppConstants.canvasGridSize,
    this.snapDistance = AppConstants.blockSnapDistance,
    this.enableSnapping = true,
    this.enableVirtualization = true,
  });

  final double gridSize;
  final double snapDistance;
  final bool enableSnapping;
  final bool enableVirtualization;

  // Canvas state
  final Map<String, BlockEntity> _blocks = {};
  final Set<String> _selectedBlockIds = {};
  Offset _canvasOffset = Offset.zero;
  double _canvasScale = 1.0;
  Size _canvasSize = Size.zero;
  Rect _viewportRect = Rect.zero;

  // Drag state
  String? _draggedBlockId;
  Offset? _dragStartPosition;
  Offset? _dragCurrentPosition;
  bool _isDragging = false;

  // History for undo/redo
  final List<CanvasState> _history = [];
  int _historyIndex = -1;
  static const int maxHistorySize = 50;

  // Getters
  Map<String, BlockEntity> get blocks => Map.unmodifiable(_blocks);
  Set<String> get selectedBlockIds => Set.unmodifiable(_selectedBlockIds);
  Offset get canvasOffset => _canvasOffset;
  double get canvasScale => _canvasScale;
  Size get canvasSize => _canvasSize;
  Rect get viewportRect => _viewportRect;
  bool get isDragging => _isDragging;
  String? get draggedBlockId => _draggedBlockId;
  bool get canUndo => _historyIndex > 0;
  bool get canRedo => _historyIndex < _history.length - 1;

  /// Add a block to the canvas
  void addBlock(BlockEntity block) {
    _saveState();
    _blocks[block.id] = block;
    notifyListeners();
  }

  /// Remove a block from the canvas
  void removeBlock(String blockId) {
    if (!_blocks.containsKey(blockId)) return;
    
    _saveState();
    _blocks.remove(blockId);
    _selectedBlockIds.remove(blockId);
    notifyListeners();
  }

  /// Update a block's properties
  void updateBlock(String blockId, BlockEntity updatedBlock) {
    if (!_blocks.containsKey(blockId)) return;
    
    _saveState();
    _blocks[blockId] = updatedBlock;
    notifyListeners();
  }

  /// Move a block to a new position
  void moveBlock(String blockId, Offset newPosition) {
    final block = _blocks[blockId];
    if (block == null) return;

    final snappedPosition = enableSnapping ? _snapToGrid(newPosition) : newPosition;
    final updatedBlock = block.copyWith(
      position: BlockPosition(
        x: snappedPosition.dx,
        y: snappedPosition.dy,
        z: block.position.z,
      ),
    );

    _blocks[blockId] = updatedBlock;
    notifyListeners();
  }

  /// Select a block
  void selectBlock(String blockId, {bool addToSelection = false}) {
    if (!addToSelection) {
      _selectedBlockIds.clear();
    }
    _selectedBlockIds.add(blockId);
    notifyListeners();
  }

  /// Deselect a block
  void deselectBlock(String blockId) {
    _selectedBlockIds.remove(blockId);
    notifyListeners();
  }

  /// Clear all selections
  void clearSelection() {
    _selectedBlockIds.clear();
    notifyListeners();
  }

  /// Start dragging a block
  void startDrag(String blockId, Offset startPosition) {
    _draggedBlockId = blockId;
    _dragStartPosition = startPosition;
    _dragCurrentPosition = startPosition;
    _isDragging = true;
    
    // Ensure the dragged block is selected
    if (!_selectedBlockIds.contains(blockId)) {
      selectBlock(blockId);
    }
    
    notifyListeners();
  }

  /// Update drag position
  void updateDrag(Offset currentPosition) {
    if (!_isDragging || _draggedBlockId == null) return;
    
    _dragCurrentPosition = currentPosition;
    
    // Calculate delta from start position
    final delta = currentPosition - _dragStartPosition!;
    
    // Move all selected blocks
    for (final blockId in _selectedBlockIds) {
      final block = _blocks[blockId];
      if (block != null) {
        final newPosition = Offset(
          block.position.x + delta.dx,
          block.position.y + delta.dy,
        );
        moveBlock(blockId, newPosition);
      }
    }
  }

  /// End dragging
  void endDrag() {
    if (_isDragging) {
      _saveState();
    }
    
    _draggedBlockId = null;
    _dragStartPosition = null;
    _dragCurrentPosition = null;
    _isDragging = false;
    notifyListeners();
  }

  /// Update canvas viewport
  void updateViewport(Offset offset, double scale, Size size) {
    _canvasOffset = offset;
    _canvasScale = scale;
    _canvasSize = size;
    _viewportRect = Rect.fromLTWH(
      -offset.dx / scale,
      -offset.dy / scale,
      size.width / scale,
      size.height / scale,
    );
    notifyListeners();
  }

  /// Get visible blocks for virtualization
  List<BlockEntity> getVisibleBlocks() {
    if (!enableVirtualization) {
      return _blocks.values.toList();
    }

    return _blocks.values.where((block) {
      final blockRect = Rect.fromLTWH(
        block.position.x,
        block.position.y,
        AppConstants.blockMinWidth,
        AppConstants.blockMinHeight,
      );
      return _viewportRect.overlaps(blockRect);
    }).toList();
  }

  /// Snap position to grid
  Offset _snapToGrid(Offset position) {
    final snappedX = (position.dx / gridSize).round() * gridSize;
    final snappedY = (position.dy / gridSize).round() * gridSize;
    return Offset(snappedX, snappedY);
  }

  /// Save current state for undo/redo
  void _saveState() {
    // Remove any states after current index (when undoing then making new changes)
    if (_historyIndex < _history.length - 1) {
      _history.removeRange(_historyIndex + 1, _history.length);
    }

    // Add current state
    _history.add(CanvasState(
      blocks: Map.from(_blocks),
      selectedBlockIds: Set.from(_selectedBlockIds),
    ));

    // Limit history size
    if (_history.length > maxHistorySize) {
      _history.removeAt(0);
    } else {
      _historyIndex++;
    }
  }

  /// Undo last action
  void undo() {
    if (!canUndo) return;

    _historyIndex--;
    final state = _history[_historyIndex];
    _blocks.clear();
    _blocks.addAll(state.blocks);
    _selectedBlockIds.clear();
    _selectedBlockIds.addAll(state.selectedBlockIds);
    notifyListeners();
  }

  /// Redo last undone action
  void redo() {
    if (!canRedo) return;

    _historyIndex++;
    final state = _history[_historyIndex];
    _blocks.clear();
    _blocks.addAll(state.blocks);
    _selectedBlockIds.clear();
    _selectedBlockIds.addAll(state.selectedBlockIds);
    notifyListeners();
  }

  /// Clear all blocks
  void clear() {
    _saveState();
    _blocks.clear();
    _selectedBlockIds.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _history.clear();
    super.dispose();
  }
}

/// Represents a snapshot of canvas state for undo/redo
class CanvasState extends Equatable {
  const CanvasState({
    required this.blocks,
    required this.selectedBlockIds,
  });

  final Map<String, BlockEntity> blocks;
  final Set<String> selectedBlockIds;

  @override
  List<Object> get props => [blocks, selectedBlockIds];
}
