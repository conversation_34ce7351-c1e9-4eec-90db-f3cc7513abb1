import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import '../../../../core/theme/design_system.dart';
import '../../../../core/canvas/canvas_controller.dart';
import '../../../../core/entities/block_entity.dart';
import '../../../../shared/widgets/atoms/app_text_field.dart';

/// Properties panel for editing selected block properties
/// 
/// Provides context-sensitive property editors based on the selected block type.
/// Supports real-time property updates with immediate visual feedback.
class PropertiesPanel extends StatefulWidget {
  const PropertiesPanel({
    super.key,
    required this.selectedBlockId,
    required this.canvasController,
    required this.onPropertyChanged,
  });

  final String? selectedBlockId;
  final CanvasController canvasController;
  final Function(String blockId, String property, dynamic value) onPropertyChanged;

  @override
  State<PropertiesPanel> createState() => _PropertiesPanelState();
}

class _PropertiesPanelState extends State<PropertiesPanel> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: widget.selectedBlockId != null
              ? _buildPropertiesContent()
              : _buildEmptyState(),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing.md),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: DesignSystem.colors.outline,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Ionicons.settings_outline,
            size: DesignSystem.components.iconMedium,
            color: DesignSystem.colors.primary,
          ),
          SizedBox(width: DesignSystem.spacing.sm),
          Text(
            'Properties',
            style: DesignSystem.typography.titleMedium.copyWith(
              color: DesignSystem.colors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Ionicons.hand_left_outline,
            size: 48,
            color: DesignSystem.colors.onSurfaceVariant,
          ),
          SizedBox(height: DesignSystem.spacing.md),
          Text(
            'Select a widget',
            style: DesignSystem.typography.titleMedium.copyWith(
              color: DesignSystem.colors.onSurfaceVariant,
            ),
          ),
          SizedBox(height: DesignSystem.spacing.sm),
          Text(
            'Click on a widget in the canvas to edit its properties',
            style: DesignSystem.typography.bodyMedium.copyWith(
              color: DesignSystem.colors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesContent() {
    final block = widget.canvasController.blocks[widget.selectedBlockId];
    if (block == null) return _buildEmptyState();

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignSystem.spacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBlockInfo(block),
          SizedBox(height: DesignSystem.spacing.lg),
          _buildPositionProperties(block),
          SizedBox(height: DesignSystem.spacing.lg),
          ..._buildTypeSpecificProperties(block),
        ],
      ),
    );
  }

  Widget _buildBlockInfo(BlockEntity block) {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing.md),
      decoration: BoxDecoration(
        color: DesignSystem.colors.primarySurface,
        borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
        border: Border.all(
          color: DesignSystem.colors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getBlockTypeLabel(block.type),
            style: DesignSystem.typography.titleSmall.copyWith(
              color: DesignSystem.colors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacing.xs),
          Text(
            'ID: ${block.id}',
            style: DesignSystem.typography.bodySmall.copyWith(
              color: DesignSystem.colors.onSurfaceVariant,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPositionProperties(BlockEntity block) {
    return _buildPropertySection(
      title: 'Position & Size',
      icon: Ionicons.move_outline,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                label: 'X',
                value: block.position.x,
                onChanged: (value) => _updatePosition(block, x: value),
              ),
            ),
            SizedBox(width: DesignSystem.spacing.sm),
            Expanded(
              child: _buildNumberField(
                label: 'Y',
                value: block.position.y,
                onChanged: (value) => _updatePosition(block, y: value),
              ),
            ),
          ],
        ),
        SizedBox(height: DesignSystem.spacing.md),
        if (block.properties.containsKey('width') || block.properties.containsKey('height'))
          Row(
            children: [
              if (block.properties.containsKey('width'))
                Expanded(
                  child: _buildNumberField(
                    label: 'Width',
                    value: block.properties['width']?.toDouble(),
                    onChanged: (value) => _updateProperty('width', value),
                  ),
                ),
              if (block.properties.containsKey('width') && block.properties.containsKey('height'))
                SizedBox(width: DesignSystem.spacing.sm),
              if (block.properties.containsKey('height'))
                Expanded(
                  child: _buildNumberField(
                    label: 'Height',
                    value: block.properties['height']?.toDouble(),
                    onChanged: (value) => _updateProperty('height', value),
                  ),
                ),
            ],
          ),
      ],
    );
  }

  List<Widget> _buildTypeSpecificProperties(BlockEntity block) {
    switch (block.type) {
      case BlockType.text:
        return [_buildTextProperties(block)];
      case BlockType.container:
        return [_buildContainerProperties(block)];
      case BlockType.button:
        return [_buildButtonProperties(block)];
      case BlockType.image:
        return [_buildImageProperties(block)];
      default:
        return [];
    }
  }

  Widget _buildTextProperties(BlockEntity block) {
    return _buildPropertySection(
      title: 'Text Properties',
      icon: Ionicons.text_outline,
      children: [
        AppTextField(
          label: 'Text',
          initialValue: block.properties['text']?.toString() ?? '',
          onChanged: (value) => _updateProperty('text', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        _buildNumberField(
          label: 'Font Size',
          value: block.properties['fontSize']?.toDouble() ?? 16.0,
          onChanged: (value) => _updateProperty('fontSize', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        _buildColorField(
          label: 'Text Color',
          value: block.properties['color']?.toInt() ?? 0xFF000000,
          onChanged: (value) => _updateProperty('color', value),
        ),
      ],
    );
  }

  Widget _buildContainerProperties(BlockEntity block) {
    return _buildPropertySection(
      title: 'Container Properties',
      icon: Ionicons.square_outline,
      children: [
        _buildColorField(
          label: 'Background Color',
          value: block.properties['backgroundColor']?.toInt() ?? 0xFFFFFFFF,
          onChanged: (value) => _updateProperty('backgroundColor', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        _buildNumberField(
          label: 'Border Radius',
          value: block.properties['borderRadius']?.toDouble() ?? 0.0,
          onChanged: (value) => _updateProperty('borderRadius', value),
        ),
      ],
    );
  }

  Widget _buildButtonProperties(BlockEntity block) {
    return _buildPropertySection(
      title: 'Button Properties',
      icon: Ionicons.radio_button_on_outline,
      children: [
        AppTextField(
          label: 'Button Text',
          initialValue: block.properties['text']?.toString() ?? 'Button',
          onChanged: (value) => _updateProperty('text', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        _buildColorField(
          label: 'Background Color',
          value: block.properties['backgroundColor']?.toInt() ?? 0xFF2196F3,
          onChanged: (value) => _updateProperty('backgroundColor', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        _buildColorField(
          label: 'Text Color',
          value: block.properties['textColor']?.toInt() ?? 0xFFFFFFFF,
          onChanged: (value) => _updateProperty('textColor', value),
        ),
      ],
    );
  }

  Widget _buildImageProperties(BlockEntity block) {
    return _buildPropertySection(
      title: 'Image Properties',
      icon: Ionicons.image_outline,
      children: [
        AppTextField(
          label: 'Image Path',
          initialValue: block.properties['imagePath']?.toString() ?? '',
          onChanged: (value) => _updateProperty('imagePath', value),
        ),
        SizedBox(height: DesignSystem.spacing.md),
        // TODO: Add image fit dropdown
      ],
    );
  }

  Widget _buildPropertySection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: DesignSystem.components.iconSmall,
              color: DesignSystem.colors.onSurfaceVariant,
            ),
            SizedBox(width: DesignSystem.spacing.xs),
            Text(
              title,
              style: DesignSystem.typography.titleSmall.copyWith(
                color: DesignSystem.colors.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: DesignSystem.spacing.md),
        ...children,
        SizedBox(height: DesignSystem.spacing.lg),
      ],
    );
  }

  Widget _buildNumberField({
    required String label,
    required double? value,
    required Function(double) onChanged,
  }) {
    final key = '${widget.selectedBlockId}_$label';
    _controllers[key] ??= TextEditingController(text: value?.toString() ?? '');
    
    return AppTextField(
      label: label,
      controller: _controllers[key],
      keyboardType: TextInputType.number,
      onChanged: (text) {
        final parsedValue = double.tryParse(text);
        if (parsedValue != null) {
          onChanged(parsedValue);
        }
      },
    );
  }

  Widget _buildColorField({
    required String label,
    required int value,
    required Function(int) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: DesignSystem.typography.labelLarge.copyWith(
            color: DesignSystem.colors.onSurface,
          ),
        ),
        SizedBox(height: DesignSystem.spacing.xs),
        GestureDetector(
          onTap: () {
            // TODO: Open color picker dialog
          },
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              color: Color(value),
              borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
              border: Border.all(
                color: DesignSystem.colors.outline,
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                '#${value.toRadixString(16).toUpperCase().padLeft(8, '0')}',
                style: DesignSystem.typography.labelMedium.copyWith(
                  color: _getContrastColor(Color(value)),
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  String _getBlockTypeLabel(BlockType type) {
    switch (type) {
      case BlockType.widget: return 'Widget';
      case BlockType.container: return 'Container';
      case BlockType.text: return 'Text';
      case BlockType.button: return 'Button';
      case BlockType.image: return 'Image';
      case BlockType.listView: return 'ListView';
      case BlockType.gridView: return 'GridView';
      case BlockType.variable: return 'Variable';
      case BlockType.function: return 'Function';
      case BlockType.condition: return 'Condition';
      case BlockType.loop: return 'Loop';
      case BlockType.event: return 'Event';
      case BlockType.apiCall: return 'API Call';
      case BlockType.database: return 'Database';
      case BlockType.storage: return 'Storage';
      case BlockType.navigation: return 'Navigation';
      case BlockType.route: return 'Route';
    }
  }

  void _updateProperty(String property, dynamic value) {
    if (widget.selectedBlockId != null) {
      widget.onPropertyChanged(widget.selectedBlockId!, property, value);
    }
  }

  void _updatePosition(BlockEntity block, {double? x, double? y}) {
    final newPosition = BlockPosition(
      x: x ?? block.position.x,
      y: y ?? block.position.y,
      z: block.position.z,
    );
    
    final updatedBlock = block.copyWith(position: newPosition);
    widget.canvasController.updateBlock(block.id, updatedBlock);
  }
}
