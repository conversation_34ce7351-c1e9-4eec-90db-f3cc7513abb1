import 'package:flutter/material.dart';

/// Modern Flat Design System for App Studio
///
/// Complete redesign following modern flat design principles inspired by
/// leading development tools like VS Code, GitHub, Figma, and Linear.
/// Emphasizes clean aesthetics, functional minimalism, and accessibility.
class DesignSystem {
  // Private constructor to prevent instantiation
  DesignSystem._();

  // ============================================================================
  // DESIGN TOKENS - Structured Token System
  // ============================================================================

  /// Spacing tokens following 8pt grid system
  static const spacing = SpacingTokens._();

  /// Typography tokens with semantic naming
  static const typography = TypographyTokens._();

  /// Color tokens with semantic and contextual meanings
  static const colors = ColorTokens._();

  /// Component tokens for consistent sizing
  static const components = ComponentTokens._();

  /// Responsive breakpoints for adaptive layouts
  static const breakpoints = ResponsiveBreakpoints._();

  // ============================================================================
  // COLOR PALETTE - Modern Development Tool Inspired
  // ============================================================================

  /// Primary Colors - Vibrant Blue for Trust & Technology
  static const Color primary = Color(0xFF0066FF);
  static const Color primaryLight = Color(0xFF3385FF);
  static const Color primaryDark = Color(0xFF0052CC);
  static const Color primarySurface = Color(0xFFF0F7FF);

  /// Secondary Colors - Semantic Color System
  static const Color success = Color(0xFF00C851);
  static const Color successLight = Color(0xFF33D474);
  static const Color successSurface = Color(0xFFF0FDF4);

  static const Color warning = Color(0xFFFF8800);
  static const Color warningLight = Color(0xFFFFAA33);
  static const Color warningSurface = Color(0xFFFFFBEB);

  static const Color error = Color(0xFFFF4444);
  static const Color errorLight = Color(0xFFFF6B6B);
  static const Color errorSurface = Color(0xFFFEF2F2);

  static const Color info = Color(0xFF33B5E5);
  static const Color infoLight = Color(0xFF5CC7EA);
  static const Color infoSurface = Color(0xFFF0F9FF);

  /// Neutral Palette - True Grays with Subtle Warm Undertones
  static const Color white = Color(0xFFFFFFFF);
  static const Color gray50 = Color(0xFFFAFBFC);
  static const Color gray100 = Color(0xFFF4F6F8);
  static const Color gray200 = Color(0xFFE4E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);
  static const Color black = Color(0xFF000000);

  // ============================================================================
  // SPACING SYSTEM - 4px Grid for Refined Mobile Design
  // ============================================================================

  static const double spacing4 = 4.0; // xs
  static const double spacing8 = 8.0; // sm
  static const double spacing12 = 12.0; // md
  static const double spacing16 = 16.0; // lg
  static const double spacing20 = 20.0; // xl
  static const double spacing24 = 24.0; // 2xl
  static const double spacing32 = 32.0; // 3xl
  static const double spacing40 = 40.0; // 4xl
  static const double spacing48 = 48.0; // 5xl
  static const double spacing64 = 64.0; // 6xl

  // ============================================================================
  // TYPOGRAPHY - Modern Flat Design Scale
  // ============================================================================

  /// Display Styles - Hero and Page Titles
  static const TextStyle displayLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 57,
    height: 1.12, // 64px line height
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 45,
    height: 1.16, // 52px line height
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 36,
    height: 1.22, // 44px line height
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
  );

  /// Headline Styles - Section Headers
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 32,
    height: 1.25, // 40px line height
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 28,
    height: 1.29, // 36px line height
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 24,
    height: 1.33, // 32px line height
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  /// Title Styles - Component Headers and Labels
  static const TextStyle titleLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 22,
    height: 1.27, // 28px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    height: 1.5, // 24px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  );

  /// Body Styles - Primary Content Text
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    height: 1.5, // 24px line height
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    height: 1.33, // 16px line height
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
  );

  /// Label Styles - Buttons and UI Elements
  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43, // 20px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    height: 1.33, // 16px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 11,
    height: 1.45, // 16px line height
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );

  // ============================================================================
  // BORDER RADIUS - Modern Flat Design
  // ============================================================================

  static const double radiusSmall = 6.0; // Buttons, chips
  static const double radiusMedium = 8.0; // Cards, containers
  static const double radiusLarge = 12.0; // Modals, sheets
  static const double radiusXLarge = 16.0; // Hero elements
  static const double radiusRound = 999.0;

  // ============================================================================
  // ELEVATION & SHADOWS - Minimal Flat Design
  // ============================================================================

  static const double elevationNone = 0.0;
  static const double elevationSmall = 1.0; // Subtle card elevation
  static const double elevationMedium = 2.0; // Button hover states
  static const double elevationLarge = 4.0; // Modals and sheets
  static const double elevationXLarge = 8.0; // Floating elements

  // ============================================================================
  // COMPONENT DIMENSIONS - Touch-Friendly Sizing
  // ============================================================================

  /// Button Heights
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;

  /// Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  static const double iconXLarge = 32.0;

  /// Touch Targets - WCAG Accessibility Compliance
  static const double touchTargetMinimum = 48.0;

  /// Responsive Breakpoints
  static const double breakpointMobile = 768.0;
  static const double breakpointTablet = 1024.0;
  static const double breakpointDesktop = 1440.0;
}

// ============================================================================
// DESIGN TOKEN CLASSES - Structured Token System
// ============================================================================

/// Spacing tokens following 8pt grid system for consistent spacing
class SpacingTokens {
  const SpacingTokens._();

  // Base spacing scale (8pt grid)
  static const double xs = 4.0; // 0.25rem
  static const double sm = 8.0; // 0.5rem
  static const double md = 16.0; // 1rem
  static const double lg = 24.0; // 1.5rem
  static const double xl = 32.0; // 2rem
  static const double xxl = 48.0; // 3rem
  static const double xxxl = 64.0; // 4rem

  // Semantic spacing
  static const double none = 0.0;
  static const double micro = 2.0;
  static const double tiny = xs;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double huge = xl;
  static const double massive = xxl;

  // Component-specific spacing
  static const double buttonPadding = md;
  static const double cardPadding = lg;
  static const double sectionPadding = xl;
  static const double screenPadding = lg;
}

/// Typography tokens with semantic naming and consistent scale
class TypographyTokens {
  const TypographyTokens._();

  // Display styles for hero content
  static const TextStyle displayLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 57,
    height: 1.12,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 45,
    height: 1.16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 36,
    height: 1.22,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
  );

  // Headline styles for section headers
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 32,
    height: 1.25,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 28,
    height: 1.29,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 24,
    height: 1.33,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.02,
  );

  // Title styles for component headers
  static const TextStyle titleLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 22,
    height: 1.27,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    height: 1.5,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  );

  // Body styles for content
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    height: 1.5,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    height: 1.33,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
  );

  // Label styles for UI elements
  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    height: 1.43,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    height: 1.33,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 11,
    height: 1.45,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );
}

/// Color tokens with semantic and contextual meanings
class ColorTokens {
  const ColorTokens._();

  // Primary brand colors
  static const Color primary = Color(0xFF0066FF);
  static const Color primaryLight = Color(0xFF3385FF);
  static const Color primaryDark = Color(0xFF0052CC);
  static const Color primarySurface = Color(0xFFF0F7FF);

  // Semantic colors
  static const Color success = Color(0xFF00C851);
  static const Color successLight = Color(0xFF33D474);
  static const Color successSurface = Color(0xFFF0FDF4);

  static const Color warning = Color(0xFFFF8800);
  static const Color warningLight = Color(0xFFFFAA33);
  static const Color warningSurface = Color(0xFFFFFBEB);

  static const Color error = Color(0xFFFF4444);
  static const Color errorLight = Color(0xFFFF6B6B);
  static const Color errorSurface = Color(0xFFFEF2F2);

  static const Color info = Color(0xFF33B5E5);
  static const Color infoLight = Color(0xFF5CC7EA);
  static const Color infoSurface = Color(0xFFF0F9FF);

  // Neutral palette
  static const Color white = Color(0xFFFFFFFF);
  static const Color gray50 = Color(0xFFFAFBFC);
  static const Color gray100 = Color(0xFFF4F6F8);
  static const Color gray200 = Color(0xFFE4E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);
  static const Color black = Color(0xFF000000);

  // Surface colors for different contexts
  static const Color surface = Color(0xFFFFFBFE);
  static const Color surfaceVariant = Color(0xFFF4F6F8);
  static const Color surfaceDim = Color(0xFFE4E7EB);
  static const Color surfaceBright = Color(0xFFFFFFFF);

  // Text colors with proper contrast
  static const Color onSurface = Color(0xFF1F2937);
  static const Color onSurfaceVariant = Color(0xFF4B5563);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onError = Color(0xFFFFFFFF);

  // Border and outline colors
  static const Color outline = Color(0xFFD1D5DB);
  static const Color outlineVariant = Color(0xFFE4E7EB);

  // Canvas and editor specific colors
  static const Color canvasBackground = Color(0xFFFAFBFC);
  static const Color canvasGrid = Color(0xFFE4E7EB);
  static const Color blockShadow = Color(0x1A000000);
  static const Color selectionHighlight = Color(0x330066FF);
}

/// Component tokens for consistent sizing and styling
class ComponentTokens {
  const ComponentTokens._();

  // Button dimensions
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonMinWidth = 64.0;

  // Icon sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 20.0;
  static const double iconLarge = 24.0;
  static const double iconXLarge = 32.0;

  // Border radius
  static const double radiusSmall = 6.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusRound = 999.0;

  // Elevation levels
  static const double elevationNone = 0.0;
  static const double elevationSmall = 1.0;
  static const double elevationMedium = 2.0;
  static const double elevationLarge = 4.0;
  static const double elevationXLarge = 8.0;

  // Touch targets (WCAG compliance)
  static const double touchTargetMinimum = 48.0;

  // Canvas and editor specific
  static const double blockMinWidth = 120.0;
  static const double blockMinHeight = 40.0;
  static const double blockSnapDistance = 20.0;
  static const double canvasGridSize = 20.0;
  static const double toolbarHeight = 56.0;
  static const double sidebarWidth = 280.0;
  static const double propertiesPanelWidth = 320.0;
}

/// Responsive breakpoints for adaptive layouts
class ResponsiveBreakpoints {
  const ResponsiveBreakpoints._();

  static const double mobile = 600.0;
  static const double tablet = 900.0;
  static const double desktop = 1200.0;
  static const double widescreen = 1600.0;

  // Helper methods for responsive checks
  static bool isMobile(double width) => width < mobile;
  static bool isTablet(double width) => width >= mobile && width < desktop;
  static bool isDesktop(double width) => width >= desktop;
  static bool isWidescreen(double width) => width >= widescreen;

  // Layout configurations
  static int getColumnsForWidth(double width) {
    if (isMobile(width)) return 1;
    if (isTablet(width)) return 2;
    if (isDesktop(width)) return 3;
    return 4; // widescreen
  }

  static double getSidebarWidth(double width) {
    if (isMobile(width)) return width * 0.8;
    if (isTablet(width)) return 280.0;
    return 320.0; // desktop and above
  }
}
