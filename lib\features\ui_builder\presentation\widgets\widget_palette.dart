import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import '../../../../core/theme/design_system.dart';
import '../../../../core/entities/block_entity.dart';

/// Widget palette for dragging UI components onto the canvas
/// 
/// Provides categorized widgets that users can drag and drop to build their UI.
/// Organized by categories: Layout, Input, Display, Navigation, etc.
class WidgetPalette extends StatefulWidget {
  const WidgetPalette({super.key});

  @override
  State<WidgetPalette> createState() => _WidgetPaletteState();
}

class _WidgetPaletteState extends State<WidgetPalette> {
  String _selectedCategory = 'Layout';
  
  final Map<String, List<PaletteWidget>> _widgetCategories = {
    'Layout': [
      PaletteWidget(
        type: BlockType.container,
        name: 'Container',
        icon: Ionicons.square_outline,
        description: 'Basic container with styling',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Column',
        icon: Ionicons.reorder_three_outline,
        description: 'Vertical layout',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Row',
        icon: Ionicons.menu_outline,
        description: 'Horizontal layout',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Stack',
        icon: Ionicons.layers_outline,
        description: 'Overlapping widgets',
      ),
    ],
    'Display': [
      PaletteWidget(
        type: BlockType.text,
        name: 'Text',
        icon: Ionicons.text_outline,
        description: 'Display text content',
      ),
      PaletteWidget(
        type: BlockType.image,
        name: 'Image',
        icon: Ionicons.image_outline,
        description: 'Display images',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Icon',
        icon: Ionicons.star_outline,
        description: 'Display icons',
      ),
    ],
    'Input': [
      PaletteWidget(
        type: BlockType.button,
        name: 'Button',
        icon: Ionicons.radio_button_on_outline,
        description: 'Clickable button',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'TextField',
        icon: Ionicons.create_outline,
        description: 'Text input field',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Switch',
        icon: Ionicons.toggle_outline,
        description: 'Toggle switch',
      ),
      PaletteWidget(
        type: BlockType.widget,
        name: 'Slider',
        icon: Ionicons.options_outline,
        description: 'Value slider',
      ),
    ],
    'Lists': [
      PaletteWidget(
        type: BlockType.listView,
        name: 'ListView',
        icon: Ionicons.list_outline,
        description: 'Scrollable list',
      ),
      PaletteWidget(
        type: BlockType.gridView,
        name: 'GridView',
        icon: Ionicons.grid_outline,
        description: 'Grid layout',
      ),
    ],
    'Navigation': [
      PaletteWidget(
        type: BlockType.navigation,
        name: 'AppBar',
        icon: Ionicons.menu_outline,
        description: 'Top navigation bar',
      ),
      PaletteWidget(
        type: BlockType.navigation,
        name: 'BottomNav',
        icon: Ionicons.ellipsis_horizontal_outline,
        description: 'Bottom navigation',
      ),
      PaletteWidget(
        type: BlockType.navigation,
        name: 'Drawer',
        icon: Ionicons.menu_outline,
        description: 'Side navigation drawer',
      ),
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildCategoryTabs(),
        Expanded(child: _buildWidgetList()),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing.md),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: DesignSystem.colors.outline,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Ionicons.apps_outline,
            size: DesignSystem.components.iconMedium,
            color: DesignSystem.colors.primary,
          ),
          SizedBox(width: DesignSystem.spacing.sm),
          Text(
            'Widgets',
            style: DesignSystem.typography.titleMedium.copyWith(
              color: DesignSystem.colors.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: DesignSystem.colors.outline,
            width: 1,
          ),
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacing.sm),
        itemCount: _widgetCategories.keys.length,
        itemBuilder: (context, index) {
          final category = _widgetCategories.keys.elementAt(index);
          final isSelected = category == _selectedCategory;
          
          return GestureDetector(
            onTap: () => setState(() => _selectedCategory = category),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacing.md,
                vertical: DesignSystem.spacing.sm,
              ),
              margin: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacing.xs,
                vertical: DesignSystem.spacing.xs,
              ),
              decoration: BoxDecoration(
                color: isSelected 
                    ? DesignSystem.colors.primary 
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
              ),
              child: Text(
                category,
                style: DesignSystem.typography.labelMedium.copyWith(
                  color: isSelected 
                      ? DesignSystem.colors.onPrimary 
                      : DesignSystem.colors.onSurfaceVariant,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWidgetList() {
    final widgets = _widgetCategories[_selectedCategory] ?? [];
    
    return ListView.builder(
      padding: EdgeInsets.all(DesignSystem.spacing.sm),
      itemCount: widgets.length,
      itemBuilder: (context, index) {
        final widget = widgets[index];
        return _buildWidgetItem(widget);
      },
    );
  }

  Widget _buildWidgetItem(PaletteWidget widget) {
    return Draggable<PaletteWidget>(
      data: widget,
      feedback: _buildDragFeedback(widget),
      childWhenDragging: _buildWidgetCard(widget, isDragging: true),
      child: _buildWidgetCard(widget),
    );
  }

  Widget _buildWidgetCard(PaletteWidget widget, {bool isDragging = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignSystem.spacing.sm),
      padding: EdgeInsets.all(DesignSystem.spacing.md),
      decoration: BoxDecoration(
        color: isDragging 
            ? DesignSystem.colors.gray200 
            : DesignSystem.colors.surface,
        borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
        border: Border.all(
          color: DesignSystem.colors.outline,
          width: 1,
        ),
        boxShadow: isDragging ? [] : [
          BoxShadow(
            color: DesignSystem.colors.blockShadow,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing.sm),
                decoration: BoxDecoration(
                  color: DesignSystem.colors.primarySurface,
                  borderRadius: BorderRadius.circular(DesignSystem.components.radiusSmall),
                ),
                child: Icon(
                  widget.icon,
                  size: DesignSystem.components.iconMedium,
                  color: DesignSystem.colors.primary,
                ),
              ),
              SizedBox(width: DesignSystem.spacing.sm),
              Expanded(
                child: Text(
                  widget.name,
                  style: DesignSystem.typography.labelLarge.copyWith(
                    color: DesignSystem.colors.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: DesignSystem.spacing.xs),
          Text(
            widget.description,
            style: DesignSystem.typography.bodySmall.copyWith(
              color: DesignSystem.colors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDragFeedback(PaletteWidget widget) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
      child: Container(
        width: 200,
        padding: EdgeInsets.all(DesignSystem.spacing.md),
        decoration: BoxDecoration(
          color: DesignSystem.colors.primarySurface,
          borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
          border: Border.all(
            color: DesignSystem.colors.primary,
            width: 2,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              widget.icon,
              size: DesignSystem.components.iconMedium,
              color: DesignSystem.colors.primary,
            ),
            SizedBox(width: DesignSystem.spacing.sm),
            Text(
              widget.name,
              style: DesignSystem.typography.labelLarge.copyWith(
                color: DesignSystem.colors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Represents a widget in the palette
class PaletteWidget {
  const PaletteWidget({
    required this.type,
    required this.name,
    required this.icon,
    required this.description,
  });

  final BlockType type;
  final String name;
  final IconData icon;
  final String description;
}
