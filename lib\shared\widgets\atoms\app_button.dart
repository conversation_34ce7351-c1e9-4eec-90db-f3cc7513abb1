import 'package:flutter/material.dart';
import '../../../core/theme/design_system.dart';

/// App Studio button component following design system tokens
/// 
/// Provides consistent styling and behavior across the application
/// with support for different variants, sizes, and states.
class AppButton extends StatelessWidget {
  const AppButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = AppButtonVariant.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.fullWidth = false,
    this.icon,
    this.iconPosition = AppButtonIconPosition.leading,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final AppButtonVariant variant;
  final AppButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final bool fullWidth;
  final IconData? icon;
  final AppButtonIconPosition iconPosition;

  @override
  Widget build(BuildContext context) {
    final isEnabled = !isDisabled && !isLoading && onPressed != null;
    
    return SizedBox(
      width: fullWidth ? double.infinity : null,
      height: _getHeight(),
      child: ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: _getButtonStyle(),
        child: isLoading ? _buildLoadingContent() : _buildContent(),
      ),
    );
  }

  double _getHeight() {
    switch (size) {
      case AppButtonSize.small:
        return DesignSystem.components.buttonHeightSmall;
      case AppButtonSize.medium:
        return DesignSystem.components.buttonHeightMedium;
      case AppButtonSize.large:
        return DesignSystem.components.buttonHeightLarge;
    }
  }

  ButtonStyle _getButtonStyle() {
    final colors = DesignSystem.colors;
    final components = DesignSystem.components;
    
    Color backgroundColor;
    Color foregroundColor;
    Color? overlayColor;
    
    switch (variant) {
      case AppButtonVariant.primary:
        backgroundColor = colors.primary;
        foregroundColor = colors.onPrimary;
        overlayColor = colors.primaryLight.withOpacity(0.1);
        break;
      case AppButtonVariant.secondary:
        backgroundColor = colors.gray100;
        foregroundColor = colors.onSurface;
        overlayColor = colors.gray200.withOpacity(0.5);
        break;
      case AppButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = colors.primary;
        overlayColor = colors.primarySurface.withOpacity(0.5);
        break;
      case AppButtonVariant.ghost:
        backgroundColor = Colors.transparent;
        foregroundColor = colors.onSurface;
        overlayColor = colors.gray100.withOpacity(0.5);
        break;
      case AppButtonVariant.destructive:
        backgroundColor = colors.error;
        foregroundColor = colors.onError;
        overlayColor = colors.errorLight.withOpacity(0.1);
        break;
    }

    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      overlayColor: overlayColor,
      elevation: variant == AppButtonVariant.outline || variant == AppButtonVariant.ghost 
          ? 0 
          : components.elevationSmall,
      shadowColor: colors.blockShadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(components.radiusMedium),
        side: variant == AppButtonVariant.outline
            ? BorderSide(color: colors.outline, width: 1)
            : BorderSide.none,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing.buttonPadding,
        vertical: 0,
      ),
      minimumSize: Size(components.buttonMinWidth, _getHeight()),
      textStyle: _getTextStyle(),
    );
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return DesignSystem.typography.labelMedium;
      case AppButtonSize.medium:
        return DesignSystem.typography.labelLarge;
      case AppButtonSize.large:
        return DesignSystem.typography.titleSmall;
    }
  }

  Widget _buildLoadingContent() {
    return SizedBox(
      height: _getIconSize(),
      width: _getIconSize(),
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          variant == AppButtonVariant.primary || variant == AppButtonVariant.destructive
              ? DesignSystem.colors.white
              : DesignSystem.colors.primary,
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (icon == null) return child;

    final iconWidget = Icon(
      icon,
      size: _getIconSize(),
    );

    final spacing = SizedBox(width: DesignSystem.spacing.sm);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: iconPosition == AppButtonIconPosition.leading
          ? [iconWidget, spacing, Flexible(child: child)]
          : [Flexible(child: child), spacing, iconWidget],
    );
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return DesignSystem.components.iconSmall;
      case AppButtonSize.medium:
        return DesignSystem.components.iconMedium;
      case AppButtonSize.large:
        return DesignSystem.components.iconLarge;
    }
  }
}

/// Button variant types
enum AppButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  destructive,
}

/// Button size options
enum AppButtonSize {
  small,
  medium,
  large,
}

/// Icon position in button
enum AppButtonIconPosition {
  leading,
  trailing,
}
