import 'package:equatable/equatable.dart';
import 'base_entity.dart';
import 'block_entity.dart';

/// Represents a Flutter app project
class ProjectEntity extends BaseEntity {
  const ProjectEntity({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.name,
    required this.description,
    required this.packageName,
    required this.version,
    required this.targetSdk,
    required this.blocks,
    required this.screens,
    required this.assets,
    required this.dependencies,
    this.settings = const ProjectSettings(),
  });

  final String name;
  final String description;
  final String packageName;
  final String version;
  final int targetSdk;
  final List<BlockEntity> blocks;
  final List<ScreenEntity> screens;
  final List<AssetEntity> assets;
  final List<DependencyEntity> dependencies;
  final ProjectSettings settings;

  @override
  List<Object?> get props => [
        ...super.props,
        name,
        description,
        packageName,
        version,
        targetSdk,
        blocks,
        screens,
        assets,
        dependencies,
        settings,
      ];

  ProjectEntity copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? packageName,
    String? version,
    int? targetSdk,
    List<BlockEntity>? blocks,
    List<ScreenEntity>? screens,
    List<AssetEntity>? assets,
    List<DependencyEntity>? dependencies,
    ProjectSettings? settings,
  }) {
    return ProjectEntity(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      packageName: packageName ?? this.packageName,
      version: version ?? this.version,
      targetSdk: targetSdk ?? this.targetSdk,
      blocks: blocks ?? this.blocks,
      screens: screens ?? this.screens,
      assets: assets ?? this.assets,
      dependencies: dependencies ?? this.dependencies,
      settings: settings ?? this.settings,
    );
  }
}

/// Represents a screen in the app
class ScreenEntity extends Equatable {
  const ScreenEntity({
    required this.id,
    required this.name,
    required this.route,
    required this.blockIds,
    this.isMainScreen = false,
  });

  final String id;
  final String name;
  final String route;
  final List<String> blockIds;
  final bool isMainScreen;

  @override
  List<Object> get props => [id, name, route, blockIds, isMainScreen];
}

/// Represents an asset in the project
class AssetEntity extends Equatable {
  const AssetEntity({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
  });

  final String id;
  final String name;
  final String path;
  final AssetType type;
  final int size;

  @override
  List<Object> get props => [id, name, path, type, size];
}

/// Asset type enumeration
enum AssetType {
  image,
  icon,
  font,
  animation,
  audio,
  video,
  data,
}

/// Represents a dependency in the project
class DependencyEntity extends Equatable {
  const DependencyEntity({
    required this.name,
    required this.version,
    this.isDevDependency = false,
  });

  final String name;
  final String version;
  final bool isDevDependency;

  @override
  List<Object> get props => [name, version, isDevDependency];
}

/// Project settings
class ProjectSettings extends Equatable {
  const ProjectSettings({
    this.theme = AppTheme.material3,
    this.primaryColor = 0xFF6750A4,
    this.useDarkMode = false,
    this.enableHotReload = true,
    this.enableDebugMode = true,
    this.minSdkVersion = 21,
    this.compileSdkVersion = 34,
  });

  final AppTheme theme;
  final int primaryColor;
  final bool useDarkMode;
  final bool enableHotReload;
  final bool enableDebugMode;
  final int minSdkVersion;
  final int compileSdkVersion;

  @override
  List<Object> get props => [
        theme,
        primaryColor,
        useDarkMode,
        enableHotReload,
        enableDebugMode,
        minSdkVersion,
        compileSdkVersion,
      ];
}

/// App theme enumeration
enum AppTheme {
  material3,
  material2,
  cupertino,
  custom,
}
