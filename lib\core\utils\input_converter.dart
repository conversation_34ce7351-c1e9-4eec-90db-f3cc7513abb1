import 'package:dartz/dartz.dart';
import '../error/failures.dart';

class InputConverter {
  Either<Failure, int> stringToUnsignedInteger(String str) {
    try {
      final integer = int.parse(str);
      if (integer < 0) throw const FormatException();
      return Right(integer);
    } on FormatException {
      return const Left(CacheFailure('Invalid input format'));
    }
  }

  Either<Failure, double> stringToDouble(String str) {
    try {
      final double value = double.parse(str);
      return Right(value);
    } on FormatException {
      return const Left(CacheFailure('Invalid number format'));
    }
  }
}
