import 'package:equatable/equatable.dart';
import 'base_entity.dart';

/// Represents a visual programming block
class BlockEntity extends BaseEntity {
  const BlockEntity({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.type,
    required this.position,
    required this.properties,
    this.connections = const [],
    this.parentId,
    this.children = const [],
  });

  final BlockType type;
  final BlockPosition position;
  final Map<String, dynamic> properties;
  final List<BlockConnection> connections;
  final String? parentId;
  final List<String> children;

  @override
  List<Object?> get props => [
        ...super.props,
        type,
        position,
        properties,
        connections,
        parentId,
        children,
      ];

  BlockEntity copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    BlockType? type,
    BlockPosition? position,
    Map<String, dynamic>? properties,
    List<BlockConnection>? connections,
    String? parentId,
    List<String>? children,
  }) {
    return BlockEntity(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      position: position ?? this.position,
      properties: properties ?? this.properties,
      connections: connections ?? this.connections,
      parentId: parentId ?? this.parentId,
      children: children ?? this.children,
    );
  }
}

/// Block type enumeration
enum BlockType {
  // UI Blocks
  widget,
  container,
  text,
  button,
  image,
  listView,
  gridView,
  
  // Logic Blocks
  variable,
  function,
  condition,
  loop,
  event,
  
  // Data Blocks
  apiCall,
  database,
  storage,
  
  // Navigation Blocks
  navigation,
  route,
}

/// Block position in the canvas
class BlockPosition extends Equatable {
  const BlockPosition({
    required this.x,
    required this.y,
    this.z = 0,
  });

  final double x;
  final double y;
  final int z; // Z-index for layering

  @override
  List<Object> get props => [x, y, z];

  BlockPosition copyWith({
    double? x,
    double? y,
    int? z,
  }) {
    return BlockPosition(
      x: x ?? this.x,
      y: y ?? this.y,
      z: z ?? this.z,
    );
  }
}

/// Connection between blocks
class BlockConnection extends Equatable {
  const BlockConnection({
    required this.sourceBlockId,
    required this.targetBlockId,
    required this.connectionType,
    this.sourcePort,
    this.targetPort,
  });

  final String sourceBlockId;
  final String targetBlockId;
  final ConnectionType connectionType;
  final String? sourcePort;
  final String? targetPort;

  @override
  List<Object?> get props => [
        sourceBlockId,
        targetBlockId,
        connectionType,
        sourcePort,
        targetPort,
      ];
}

/// Connection type enumeration
enum ConnectionType {
  dataFlow,
  controlFlow,
  parent,
  child,
}
