import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../theme/design_system.dart';
import '../entities/block_entity.dart';
import 'canvas_controller.dart';

/// Interactive canvas widget for visual block editing
/// 
/// Provides pan, zoom, selection, and drag-and-drop functionality
/// with grid background and performance optimizations.
class CanvasWidget extends StatefulWidget {
  const CanvasWidget({
    super.key,
    required this.controller,
    this.onBlockTap,
    this.onBlockDoubleTap,
    this.onCanvasTap,
    this.showGrid = true,
    this.backgroundColor,
    this.gridColor,
  });

  final CanvasController controller;
  final Function(String blockId)? onBlockTap;
  final Function(String blockId)? onBlockDoubleTap;
  final Function(Offset position)? onCanvasTap;
  final bool showGrid;
  final Color? backgroundColor;
  final Color? gridColor;

  @override
  State<CanvasWidget> createState() => _CanvasWidgetState();
}

class _CanvasWidgetState extends State<CanvasWidget> {
  final TransformationController _transformationController = TransformationController();
  
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onCanvasChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onCanvasChanged);
    _transformationController.dispose();
    super.dispose();
  }

  void _onCanvasChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.backgroundColor ?? DesignSystem.colors.canvasBackground,
      child: InteractiveViewer(
        transformationController: _transformationController,
        boundaryMargin: const EdgeInsets.all(double.infinity),
        minScale: 0.1,
        maxScale: 5.0,
        onInteractionStart: _onInteractionStart,
        onInteractionUpdate: _onInteractionUpdate,
        onInteractionEnd: _onInteractionEnd,
        child: GestureDetector(
          onTapDown: _onCanvasTapDown,
          child: CustomPaint(
            painter: widget.showGrid ? CanvasGridPainter(
              gridSize: widget.controller.gridSize,
              gridColor: widget.gridColor ?? DesignSystem.colors.canvasGrid,
            ) : null,
            child: SizedBox(
              width: 5000, // Large canvas size
              height: 5000,
              child: Stack(
                children: _buildBlocks(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildBlocks() {
    final visibleBlocks = widget.controller.getVisibleBlocks();
    
    return visibleBlocks.map((block) {
      return Positioned(
        left: block.position.x,
        top: block.position.y,
        child: BlockWidget(
          key: ValueKey(block.id),
          block: block,
          isSelected: widget.controller.selectedBlockIds.contains(block.id),
          isDragging: widget.controller.draggedBlockId == block.id,
          onTap: () => widget.onBlockTap?.call(block.id),
          onDoubleTap: () => widget.onBlockDoubleTap?.call(block.id),
          onDragStart: (details) => _onBlockDragStart(block.id, details),
          onDragUpdate: (details) => _onBlockDragUpdate(details),
          onDragEnd: (details) => _onBlockDragEnd(details),
        ),
      );
    }).toList();
  }

  void _onInteractionStart(ScaleStartDetails details) {
    // Update viewport information
    _updateViewport();
  }

  void _onInteractionUpdate(ScaleUpdateDetails details) {
    // Update viewport information during pan/zoom
    _updateViewport();
  }

  void _onInteractionEnd(ScaleEndDetails details) {
    // Final viewport update
    _updateViewport();
  }

  void _updateViewport() {
    final transform = _transformationController.value;
    final translation = transform.getTranslation();
    final scale = transform.getMaxScaleOnAxis();
    
    widget.controller.updateViewport(
      Offset(translation.x, translation.y),
      scale,
      context.size ?? Size.zero,
    );
  }

  void _onCanvasTapDown(TapDownDetails details) {
    // Clear selection when tapping on empty canvas
    widget.controller.clearSelection();
    widget.onCanvasTap?.call(details.localPosition);
  }

  void _onBlockDragStart(String blockId, DragStartDetails details) {
    widget.controller.startDrag(blockId, details.localPosition);
  }

  void _onBlockDragUpdate(DragUpdateDetails details) {
    widget.controller.updateDrag(details.localPosition);
  }

  void _onBlockDragEnd(DragEndDetails details) {
    widget.controller.endDrag();
  }
}

/// Individual block widget with drag and selection support
class BlockWidget extends StatelessWidget {
  const BlockWidget({
    super.key,
    required this.block,
    required this.isSelected,
    required this.isDragging,
    this.onTap,
    this.onDoubleTap,
    this.onDragStart,
    this.onDragUpdate,
    this.onDragEnd,
  });

  final BlockEntity block;
  final bool isSelected;
  final bool isDragging;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final Function(DragStartDetails)? onDragStart;
  final Function(DragUpdateDetails)? onDragUpdate;
  final Function(DragEndDetails)? onDragEnd;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onDoubleTap: onDoubleTap,
      child: Draggable<String>(
        data: block.id,
        onDragStarted: () => onDragStart?.call(DragStartDetails()),
        onDragUpdate: onDragUpdate,
        onDragEnd: onDragEnd,
        feedback: _buildBlockContent(isDragging: true),
        childWhenDragging: _buildBlockContent(isGhost: true),
        child: _buildBlockContent(),
      ),
    );
  }

  Widget _buildBlockContent({bool isDragging = false, bool isGhost = false}) {
    final colors = DesignSystem.colors;
    final components = DesignSystem.components;
    
    Color backgroundColor;
    Color borderColor;
    double opacity = 1.0;
    
    if (isGhost) {
      backgroundColor = colors.gray200;
      borderColor = colors.gray300;
      opacity = 0.5;
    } else if (isDragging) {
      backgroundColor = colors.primarySurface;
      borderColor = colors.primary;
      opacity = 0.9;
    } else if (isSelected) {
      backgroundColor = colors.surface;
      borderColor = colors.primary;
    } else {
      backgroundColor = colors.surface;
      borderColor = colors.outline;
    }

    return Opacity(
      opacity: opacity,
      child: Container(
        width: 120, // TODO: Calculate based on block content
        height: 60,  // TODO: Calculate based on block content
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(
            color: borderColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(components.radiusMedium),
          boxShadow: isDragging ? [
            BoxShadow(
              color: colors.blockShadow,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : [
            BoxShadow(
              color: colors.blockShadow,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(DesignSystem.spacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getBlockTypeLabel(),
                style: DesignSystem.typography.labelMedium.copyWith(
                  color: colors.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (block.properties.isNotEmpty) ...[
                SizedBox(height: DesignSystem.spacing.xs),
                Text(
                  _getBlockDescription(),
                  style: DesignSystem.typography.bodySmall.copyWith(
                    color: colors.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getBlockTypeLabel() {
    switch (block.type) {
      case BlockType.widget:
        return 'Widget';
      case BlockType.container:
        return 'Container';
      case BlockType.text:
        return 'Text';
      case BlockType.button:
        return 'Button';
      case BlockType.image:
        return 'Image';
      case BlockType.listView:
        return 'ListView';
      case BlockType.gridView:
        return 'GridView';
      case BlockType.variable:
        return 'Variable';
      case BlockType.function:
        return 'Function';
      case BlockType.condition:
        return 'If/Else';
      case BlockType.loop:
        return 'Loop';
      case BlockType.event:
        return 'Event';
      case BlockType.apiCall:
        return 'API Call';
      case BlockType.database:
        return 'Database';
      case BlockType.storage:
        return 'Storage';
      case BlockType.navigation:
        return 'Navigate';
      case BlockType.route:
        return 'Route';
    }
  }

  String _getBlockDescription() {
    // TODO: Generate description based on block properties
    return block.properties.toString();
  }
}

/// Custom painter for canvas grid
class CanvasGridPainter extends CustomPainter {
  const CanvasGridPainter({
    required this.gridSize,
    required this.gridColor,
  });

  final double gridSize;
  final Color gridColor;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = gridColor
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CanvasGridPainter oldDelegate) {
    return oldDelegate.gridSize != gridSize || oldDelegate.gridColor != gridColor;
  }
}
