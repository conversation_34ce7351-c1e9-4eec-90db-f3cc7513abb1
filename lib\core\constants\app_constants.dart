class AppConstants {
  // App Information
  static const String appName = 'App Studio';
  static const String appDescription =
      'Visual Flutter App Development Platform';
  static const String appVersion = '0.1.0';

  // API Configuration
  static const String apiBaseUrl = 'https://api.appstudio.dev';
  static const int connectionTimeout = 30000;
  static const int receiveTimeout = 30000;

  // Storage keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String projectsKey = 'projects';
  static const String settingsKey = 'settings';
  static const String themeKey = 'theme_settings';

  // Error messages
  static const String networkError = 'Network connection failed';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'Unknown error occurred';
  static const String projectLoadError = 'Failed to load project';
  static const String projectSaveError = 'Failed to save project';
  static const String codeGenerationError = 'Failed to generate code';

  // File Extensions
  static const String projectFileExtension = '.appstudio';
  static const String dartFileExtension = '.dart';
  static const String jsonFileExtension = '.json';

  // Default Values
  static const int defaultTargetSdk = 34;
  static const int defaultMinSdk = 21;
  static const String defaultPackagePrefix = 'com.appstudio.';

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 12.0;
  static const double defaultElevation = 1.0;

  // Block Editor Constants
  static const double blockMinWidth = 120.0;
  static const double blockMinHeight = 40.0;
  static const double blockSnapDistance = 20.0;
  static const double canvasGridSize = 20.0;
}
