import 'package:flutter/material.dart';
import '../entities/block_entity.dart';
import '../entities/project_entity.dart';

/// Preview engine for real-time app rendering
/// 
/// Converts block entities into live Flutter widgets for immediate preview
/// with support for hot reload-like updates and error handling.
class PreviewEngine {
  PreviewEngine({
    this.enableHotReload = true,
    this.showDebugInfo = false,
  });

  final bool enableHotReload;
  final bool showDebugInfo;

  /// Render a complete screen from blocks
  Widget renderScreen({
    required List<BlockEntity> blocks,
    required ProjectSettings settings,
    String? screenId,
  }) {
    try {
      // Find root blocks (blocks without parents)
      final rootBlocks = blocks.where((block) => block.parentId == null).toList();
      
      // Sort by z-index for proper layering
      rootBlocks.sort((a, b) => a.position.z.compareTo(b.position.z));

      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: _buildThemeData(settings),
        home: Scaffold(
          body: Stack(
            children: rootBlocks.map((block) => _renderBlock(block, blocks)).toList(),
          ),
        ),
      );
    } catch (error) {
      return _buildErrorWidget(error);
    }
  }

  /// Render a single block and its children
  Widget _renderBlock(BlockEntity block, List<BlockEntity> allBlocks) {
    try {
      // Get child blocks
      final childBlocks = allBlocks
          .where((b) => b.parentId == block.id)
          .toList()
        ..sort((a, b) => a.position.z.compareTo(b.position.z));

      // Position the block
      return Positioned(
        left: block.position.x,
        top: block.position.y,
        child: _buildBlockWidget(block, childBlocks, allBlocks),
      );
    } catch (error) {
      return _buildBlockErrorWidget(block, error);
    }
  }

  /// Build widget based on block type
  Widget _buildBlockWidget(
    BlockEntity block,
    List<BlockEntity> childBlocks,
    List<BlockEntity> allBlocks,
  ) {
    switch (block.type) {
      case BlockType.container:
        return _buildContainer(block, childBlocks, allBlocks);
      case BlockType.text:
        return _buildText(block);
      case BlockType.button:
        return _buildButton(block);
      case BlockType.image:
        return _buildImage(block);
      case BlockType.listView:
        return _buildListView(block, childBlocks, allBlocks);
      case BlockType.gridView:
        return _buildGridView(block, childBlocks, allBlocks);
      default:
        return _buildPlaceholder(block);
    }
  }

  Widget _buildContainer(
    BlockEntity block,
    List<BlockEntity> childBlocks,
    List<BlockEntity> allBlocks,
  ) {
    final properties = block.properties;
    
    return Container(
      width: _getDoubleProperty(properties, 'width'),
      height: _getDoubleProperty(properties, 'height'),
      padding: _getEdgeInsetsProperty(properties, 'padding'),
      margin: _getEdgeInsetsProperty(properties, 'margin'),
      decoration: BoxDecoration(
        color: _getColorProperty(properties, 'backgroundColor'),
        borderRadius: BorderRadius.circular(
          _getDoubleProperty(properties, 'borderRadius') ?? 0,
        ),
        border: _getBorderProperty(properties),
      ),
      child: childBlocks.isEmpty
          ? null
          : Stack(
              children: childBlocks
                  .map((child) => _renderBlock(child, allBlocks))
                  .toList(),
            ),
    );
  }

  Widget _buildText(BlockEntity block) {
    final properties = block.properties;
    
    return Text(
      _getStringProperty(properties, 'text') ?? 'Text',
      style: TextStyle(
        fontSize: _getDoubleProperty(properties, 'fontSize') ?? 16,
        fontWeight: _getFontWeightProperty(properties, 'fontWeight'),
        color: _getColorProperty(properties, 'color') ?? Colors.black,
        fontFamily: _getStringProperty(properties, 'fontFamily'),
      ),
      textAlign: _getTextAlignProperty(properties, 'textAlign'),
      maxLines: _getIntProperty(properties, 'maxLines'),
      overflow: _getTextOverflowProperty(properties, 'overflow'),
    );
  }

  Widget _buildButton(BlockEntity block) {
    final properties = block.properties;
    
    return ElevatedButton(
      onPressed: () {
        // TODO: Handle button press events
        debugPrint('Button pressed: ${block.id}');
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: _getColorProperty(properties, 'backgroundColor'),
        foregroundColor: _getColorProperty(properties, 'textColor'),
        padding: _getEdgeInsetsProperty(properties, 'padding'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            _getDoubleProperty(properties, 'borderRadius') ?? 8,
          ),
        ),
      ),
      child: Text(_getStringProperty(properties, 'text') ?? 'Button'),
    );
  }

  Widget _buildImage(BlockEntity block) {
    final properties = block.properties;
    final imagePath = _getStringProperty(properties, 'imagePath');
    
    if (imagePath == null) {
      return Container(
        width: _getDoubleProperty(properties, 'width') ?? 100,
        height: _getDoubleProperty(properties, 'height') ?? 100,
        color: Colors.grey[300],
        child: const Icon(Icons.image, size: 48, color: Colors.grey),
      );
    }

    return Image.asset(
      imagePath,
      width: _getDoubleProperty(properties, 'width'),
      height: _getDoubleProperty(properties, 'height'),
      fit: _getBoxFitProperty(properties, 'fit'),
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: _getDoubleProperty(properties, 'width') ?? 100,
          height: _getDoubleProperty(properties, 'height') ?? 100,
          color: Colors.red[100],
          child: const Icon(Icons.error, color: Colors.red),
        );
      },
    );
  }

  Widget _buildListView(
    BlockEntity block,
    List<BlockEntity> childBlocks,
    List<BlockEntity> allBlocks,
  ) {
    final properties = block.properties;
    
    return SizedBox(
      width: _getDoubleProperty(properties, 'width'),
      height: _getDoubleProperty(properties, 'height') ?? 200,
      child: ListView.builder(
        scrollDirection: _getAxisProperty(properties, 'scrollDirection') ?? Axis.vertical,
        itemCount: childBlocks.length,
        itemBuilder: (context, index) {
          return _buildBlockWidget(childBlocks[index], [], allBlocks);
        },
      ),
    );
  }

  Widget _buildGridView(
    BlockEntity block,
    List<BlockEntity> childBlocks,
    List<BlockEntity> allBlocks,
  ) {
    final properties = block.properties;
    
    return SizedBox(
      width: _getDoubleProperty(properties, 'width'),
      height: _getDoubleProperty(properties, 'height') ?? 200,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _getIntProperty(properties, 'crossAxisCount') ?? 2,
          crossAxisSpacing: _getDoubleProperty(properties, 'crossAxisSpacing') ?? 8,
          mainAxisSpacing: _getDoubleProperty(properties, 'mainAxisSpacing') ?? 8,
        ),
        itemCount: childBlocks.length,
        itemBuilder: (context, index) {
          return _buildBlockWidget(childBlocks[index], [], allBlocks);
        },
      ),
    );
  }

  Widget _buildPlaceholder(BlockEntity block) {
    return Container(
      width: 100,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          block.type.name.toUpperCase(),
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  ThemeData _buildThemeData(ProjectSettings settings) {
    return ThemeData(
      useMaterial3: settings.theme == AppTheme.material3,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Color(settings.primaryColor),
        brightness: settings.useDarkMode ? Brightness.dark : Brightness.light,
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text(
                'Preview Error',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBlockErrorWidget(BlockEntity block, Object error) {
    return Container(
      width: 120,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.red[100],
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error, size: 16, color: Colors.red),
            Text(
              'Error',
              style: TextStyle(fontSize: 10, color: Colors.red[700]),
            ),
          ],
        ),
      ),
    );
  }

  // Property extraction helpers
  String? _getStringProperty(Map<String, dynamic> properties, String key) {
    return properties[key] as String?;
  }

  double? _getDoubleProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key];
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  int? _getIntProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key];
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  Color? _getColorProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key];
    if (value is int) return Color(value);
    if (value is String) {
      // Handle hex color strings
      if (value.startsWith('#')) {
        return Color(int.parse(value.substring(1), radix: 16) + 0xFF000000);
      }
    }
    return null;
  }

  EdgeInsets? _getEdgeInsetsProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key];
    if (value is double) return EdgeInsets.all(value);
    if (value is Map) {
      return EdgeInsets.only(
        left: (value['left'] as num?)?.toDouble() ?? 0,
        top: (value['top'] as num?)?.toDouble() ?? 0,
        right: (value['right'] as num?)?.toDouble() ?? 0,
        bottom: (value['bottom'] as num?)?.toDouble() ?? 0,
      );
    }
    return null;
  }

  FontWeight? _getFontWeightProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key];
    if (value is int) {
      switch (value) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
      }
    }
    return null;
  }

  TextAlign? _getTextAlignProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key] as String?;
    switch (value) {
      case 'left': return TextAlign.left;
      case 'center': return TextAlign.center;
      case 'right': return TextAlign.right;
      case 'justify': return TextAlign.justify;
      default: return null;
    }
  }

  TextOverflow? _getTextOverflowProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key] as String?;
    switch (value) {
      case 'clip': return TextOverflow.clip;
      case 'ellipsis': return TextOverflow.ellipsis;
      case 'fade': return TextOverflow.fade;
      default: return null;
    }
  }

  BoxFit? _getBoxFitProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key] as String?;
    switch (value) {
      case 'fill': return BoxFit.fill;
      case 'contain': return BoxFit.contain;
      case 'cover': return BoxFit.cover;
      case 'fitWidth': return BoxFit.fitWidth;
      case 'fitHeight': return BoxFit.fitHeight;
      default: return null;
    }
  }

  Axis? _getAxisProperty(Map<String, dynamic> properties, String key) {
    final value = properties[key] as String?;
    switch (value) {
      case 'horizontal': return Axis.horizontal;
      case 'vertical': return Axis.vertical;
      default: return null;
    }
  }

  Border? _getBorderProperty(Map<String, dynamic> properties) {
    final borderWidth = _getDoubleProperty(properties, 'borderWidth');
    final borderColor = _getColorProperty(properties, 'borderColor');
    
    if (borderWidth != null && borderColor != null) {
      return Border.all(width: borderWidth, color: borderColor);
    }
    return null;
  }
}
