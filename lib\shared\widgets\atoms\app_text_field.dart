import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/design_system.dart';

/// App Studio text field component following design system tokens
/// 
/// Provides consistent styling and behavior for text input across the application
/// with support for different variants, validation states, and input types.
class AppTextField extends StatefulWidget {
  const AppTextField({
    super.key,
    this.controller,
    this.initialValue,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.keyboardType,
    this.textInputAction,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.focusNode,
    this.variant = AppTextFieldVariant.outlined,
    this.size = AppTextFieldSize.medium,
  });

  final TextEditingController? controller;
  final String? initialValue;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final FocusNode? focusNode;
  final AppTextFieldVariant variant;
  final AppTextFieldSize size;

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: DesignSystem.typography.labelLarge.copyWith(
              color: DesignSystem.colors.onSurface,
            ),
          ),
          SizedBox(height: DesignSystem.spacing.xs),
        ],
        TextFormField(
          controller: widget.controller,
          initialValue: widget.initialValue,
          focusNode: _focusNode,
          obscureText: widget.obscureText,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          inputFormatters: widget.inputFormatters,
          validator: widget.validator,
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onSubmitted,
          onTap: widget.onTap,
          style: _getTextStyle(),
          decoration: _getInputDecoration(),
        ),
        if (widget.helperText != null || widget.errorText != null) ...[
          SizedBox(height: DesignSystem.spacing.xs),
          Text(
            widget.errorText ?? widget.helperText!,
            style: DesignSystem.typography.bodySmall.copyWith(
              color: widget.errorText != null
                  ? DesignSystem.colors.error
                  : DesignSystem.colors.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return DesignSystem.typography.bodySmall;
      case AppTextFieldSize.medium:
        return DesignSystem.typography.bodyMedium;
      case AppTextFieldSize.large:
        return DesignSystem.typography.bodyLarge;
    }
  }

  InputDecoration _getInputDecoration() {
    final colors = DesignSystem.colors;
    final components = DesignSystem.components;
    final hasError = widget.errorText != null;

    Color borderColor;
    if (hasError) {
      borderColor = colors.error;
    } else if (_isFocused) {
      borderColor = colors.primary;
    } else {
      borderColor = colors.outline;
    }

    Color fillColor;
    switch (widget.variant) {
      case AppTextFieldVariant.outlined:
        fillColor = colors.surface;
        break;
      case AppTextFieldVariant.filled:
        fillColor = colors.surfaceVariant;
        break;
    }

    return InputDecoration(
      hintText: widget.hint,
      hintStyle: DesignSystem.typography.bodyMedium.copyWith(
        color: colors.onSurfaceVariant,
      ),
      prefixIcon: widget.prefixIcon != null
          ? Icon(
              widget.prefixIcon,
              size: _getIconSize(),
              color: _isFocused ? colors.primary : colors.onSurfaceVariant,
            )
          : null,
      suffixIcon: widget.suffixIcon,
      filled: true,
      fillColor: fillColor,
      contentPadding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing.md,
        vertical: _getVerticalPadding(),
      ),
      border: _getBorder(colors.outline),
      enabledBorder: _getBorder(colors.outline),
      focusedBorder: _getBorder(colors.primary),
      errorBorder: _getBorder(colors.error),
      focusedErrorBorder: _getBorder(colors.error),
      disabledBorder: _getBorder(colors.outlineVariant),
      counterStyle: DesignSystem.typography.bodySmall.copyWith(
        color: colors.onSurfaceVariant,
      ),
    );
  }

  OutlineInputBorder _getBorder(Color color) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignSystem.components.radiusMedium),
      borderSide: BorderSide(
        color: color,
        width: 1,
      ),
    );
  }

  double _getIconSize() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return DesignSystem.components.iconSmall;
      case AppTextFieldSize.medium:
        return DesignSystem.components.iconMedium;
      case AppTextFieldSize.large:
        return DesignSystem.components.iconLarge;
    }
  }

  double _getVerticalPadding() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return DesignSystem.spacing.sm;
      case AppTextFieldSize.medium:
        return DesignSystem.spacing.md;
      case AppTextFieldSize.large:
        return DesignSystem.spacing.lg;
    }
  }
}

/// Text field variant types
enum AppTextFieldVariant {
  outlined,
  filled,
}

/// Text field size options
enum AppTextFieldSize {
  small,
  medium,
  large,
}
